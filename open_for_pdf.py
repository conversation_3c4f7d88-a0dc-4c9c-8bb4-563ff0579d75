#!/usr/bin/env python3
"""
Simple script to open the resume in browser for PDF creation
"""

import os
import webbrowser
from pathlib import Path

def main():
    """Open the resume in browser for PDF creation"""
    
    # Get the current directory
    current_dir = Path.cwd()
    html_file = current_dir / "Raid_Fodeh_Resume_Print.html"
    
    if not html_file.exists():
        print(f"❌ HTML file not found: {html_file}")
        return
    
    # Convert to file URL
    file_url = f"file://{html_file.absolute()}"
    
    print("🌐 Opening resume in browser...")
    print(f"📄 File: {html_file}")
    print(f"🔗 URL: {file_url}")
    
    # Open in browser
    webbrowser.open(file_url)
    
    print("\n📋 TO CREATE PDF:")
    print("1. Press Cmd+P (Command + P)")
    print("2. Select 'Save as PDF' as destination")
    print("3. Set margins to 'Minimum' or 0.5 inches")
    print("4. Enable 'Background graphics'")
    print("5. Disable 'Headers and footers'")
    print("6. Set scale to 100%")
    print("7. Save as 'Raid_Fodeh_Resume.pdf'")
    print("\n✅ Your resume will be saved as a professional PDF!")

if __name__ == "__main__":
    main()
