#!/usr/bin/env python3
"""
PDF Creator using pdfkit (wkhtmltopdf wrapper)
"""

import os
import subprocess
import sys

def install_pdfkit():
    """Install pdfkit"""
    try:
        import pdfkit
        return True
    except ImportError:
        print("Installing pdfkit...")
        os.system("pip3 install pdfkit")
        try:
            import pdfkit
            return True
        except ImportError:
            return False

def check_wkhtmltopdf():
    """Check if wkhtmltopdf is installed"""
    try:
        result = subprocess.run(['wkhtmltopdf', '--version'], 
                              capture_output=True, text=True)
        return result.returncode == 0
    except FileNotFoundError:
        return False

def install_wkhtmltopdf_mac():
    """Install wkhtmltopdf on macOS"""
    print("Installing wkhtmltopdf...")
    try:
        # Try homebrew first
        result = subprocess.run(['brew', '--version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("Using Homebrew to install wkhtmltopdf...")
            os.system("brew install wkhtmltopdf")
            return True
    except FileNotFoundError:
        pass
    
    print("Please install wkhtmltopdf manually:")
    print("1. Visit: https://wkhtmltopdf.org/downloads.html")
    print("2. Download the macOS version")
    print("3. Install the .pkg file")
    return False

def create_pdf_with_pdfkit():
    """Create PDF using pdfkit"""
    try:
        import pdfkit
        
        # Configuration for better PDF output
        options = {
            'page-size': 'A4',
            'margin-top': '0.5in',
            'margin-right': '0.5in',
            'margin-bottom': '0.5in',
            'margin-left': '0.5in',
            'encoding': "UTF-8",
            'no-outline': None,
            'enable-local-file-access': None,
            'print-media-type': None
        }
        
        html_file = "Raid_Fodeh_Resume_Print.html"
        pdf_file = "Raid_Fodeh_Resume.pdf"
        
        if not os.path.exists(html_file):
            print(f"❌ HTML file not found: {html_file}")
            return False
        
        print("🔄 Converting HTML to PDF...")
        pdfkit.from_file(html_file, pdf_file, options=options)
        print(f"✅ PDF created successfully: {pdf_file}")
        return True
        
    except Exception as e:
        print(f"❌ Error creating PDF: {e}")
        return False

def main():
    """Main function"""
    print("🚀 Creating PDF from HTML...")
    
    # Check if pdfkit is available
    if not install_pdfkit():
        print("❌ Failed to install pdfkit")
        return
    
    # Check if wkhtmltopdf is installed
    if not check_wkhtmltopdf():
        print("❌ wkhtmltopdf not found")
        if not install_wkhtmltopdf_mac():
            print("\n📄 Alternative: Use browser method")
            print("1. Open the HTML file in your browser")
            print("2. Press Cmd+P to print")
            print("3. Save as PDF")
            return
    
    # Create PDF
    if create_pdf_with_pdfkit():
        print("\n✅ PDF creation completed!")
        print("📁 File created: Raid_Fodeh_Resume.pdf")
    else:
        print("\n📄 Please use browser method:")
        print("1. Open Raid_Fodeh_Resume_Print.html in your browser")
        print("2. Press Cmd+P to print")
        print("3. Save as PDF")

if __name__ == "__main__":
    main()
