#!/usr/bin/env python3
"""
Resume Converter Script
Converts HTML resume to Word format and creates a print-ready HTML
"""

import os
import sys
from pathlib import Path

def install_requirements():
    """Install required packages"""
    packages = [
        'python-docx',
        'beautifulsoup4',
        'lxml'
    ]

    for package in packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            print(f"Installing {package}...")
            os.system(f"pip3 install {package}")

def create_print_html(html_file, print_html_file):
    """Create a print-optimized HTML version"""
    try:
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # Add print-specific CSS
        print_css = """
        <style media="print">
            @page {
                size: A4;
                margin: 0.3in;
            }

            * {
                margin: 0 !important;
                padding: 0 !important;
            }

            body {
                font-size: 9pt !important;
                line-height: 1.1 !important;
                font-family: Arial, sans-serif !important;
            }

            .container {
                max-width: none !important;
            }

            .header {
                margin-bottom: 4px !important;
            }

            .header h1 {
                font-size: 16pt !important;
                margin-bottom: 1px !important;
                font-weight: bold !important;
            }

            .contact-info {
                font-size: 8.5pt !important;
                margin-bottom: 4px !important;
                line-height: 1.0 !important;
            }

            .page-break {
                page-break-before: always !important;
            }

            .section {
                margin-bottom: 4px !important;
            }

            .section-title {
                font-size: 10pt !important;
                font-weight: bold !important;
                margin-bottom: 2px !important;
                padding-bottom: 1px !important;
                border-bottom: 1px solid #000 !important;
            }

            .job-content {
                margin-bottom: 3px !important;
            }

            .job-title {
                font-size: 9.5pt !important;
                font-weight: bold !important;
                margin-bottom: 0px !important;
            }

            .company-info {
                font-size: 8.5pt !important;
                font-style: italic !important;
                margin-bottom: 1px !important;
            }

            ul {
                margin: 0px 0 2px 0 !important;
                padding-left: 12px !important;
            }

            li {
                margin-bottom: 0px !important;
                font-size: 8.5pt !important;
                line-height: 1.05 !important;
            }

            .summary {
                margin-bottom: 4px !important;
                font-size: 9pt !important;
                line-height: 1.1 !important;
            }

            .no-print {
                display: none !important;
            }

            /* Two-column layout for competencies */
            .competencies ul {
                columns: 2 !important;
                column-gap: 15px !important;
                margin: 0 !important;
            }

            .competencies li {
                break-inside: avoid !important;
                margin-bottom: 0 !important;
                font-size: 8.5pt !important;
            }

            /* Compact technical skills */
            .section ul li strong {
                font-size: 8.5pt !important;
            }

            /* Minimize all spacing */
            h1, h2, h3, h4, h5, h6 {
                margin: 0 !important;
                padding: 0 !important;
            }

            p {
                margin: 0 !important;
                padding: 0 !important;
            }
        </style>
        """

        # Insert print CSS before closing head tag
        content = content.replace('</head>', print_css + '</head>')

        with open(print_html_file, 'w', encoding='utf-8') as f:
            f.write(content)

        print(f"✅ Print-ready HTML created: {print_html_file}")
        print("📄 To create PDF: Open this file in your browser and print to PDF")
        return True
    except Exception as e:
        print(f"❌ Error creating print HTML: {e}")
        return False

def html_to_docx(html_file, docx_file):
    """Convert HTML to Word document"""
    try:
        from docx import Document
        from docx.shared import Inches, Pt
        from docx.enum.text import WD_ALIGN_PARAGRAPH
        from docx.oxml.shared import OxmlElement, qn
        from bs4 import BeautifulSoup
        
        # Read HTML content
        with open(html_file, 'r', encoding='utf-8') as f:
            soup = BeautifulSoup(f.read(), 'html.parser')
        
        # Create Word document
        doc = Document()
        
        # Set document margins
        sections = doc.sections
        for section in sections:
            section.top_margin = Inches(0.5)
            section.bottom_margin = Inches(0.5)
            section.left_margin = Inches(0.75)
            section.right_margin = Inches(0.75)
        
        # Header - Name and Contact Info
        header = doc.add_paragraph()
        header.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        name_run = header.add_run("RAID FODEH")
        name_run.font.size = Pt(20)
        name_run.font.bold = True
        name_run.font.name = 'Calibri'
        
        header.add_run("\n")
        contact_run = header.add_run("Al-Majaz, Sharjah, UAE | +971 58 595 3088 | <EMAIL>\nJordanian National | Born August 1969 | Married, 3 Children")
        contact_run.font.size = Pt(9)
        contact_run.font.name = 'Calibri'
        
        # Add horizontal line
        doc.add_paragraph("_" * 80).alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Professional Summary
        summary_heading = doc.add_paragraph()
        summary_run = summary_heading.add_run("PROFESSIONAL SUMMARY")
        summary_run.font.bold = True
        summary_run.font.size = Pt(12)
        summary_run.font.name = 'Calibri'
        
        summary_text = doc.add_paragraph()
        summary_content = summary_text.add_run(
            "Dynamic and results-driven executive with over 30 years of experience across the Middle East and Latin America, "
            "delivering operational excellence, logistics optimization, and commercial growth. Proven track record of leading "
            "large multicultural teams (1,300+ employees), managing complex logistics ecosystems, and driving strategic initiatives. "
            "Recently enhanced technical competencies with Python, Flutter, WordPress, and AI automation, building scalable digital "
            "tools and platforms. A hybrid leader combining business insight with modern tech-driven innovation."
        )
        summary_content.font.size = Pt(10)
        summary_content.font.name = 'Calibri'
        
        # Core Competencies
        comp_heading = doc.add_paragraph()
        comp_run = comp_heading.add_run("CORE COMPETENCIES")
        comp_run.font.bold = True
        comp_run.font.size = Pt(12)
        comp_run.font.name = 'Calibri'
        
        competencies = [
            "Strategic Business Planning & Execution",
            "Operations & Logistics Optimization", 
            "P&L Management & Revenue Growth",
            "Team Leadership & Staff Development",
            "Contract Negotiation & Client Relations",
            "Project & Resource Management",
            "Cross-border B2B Sales (MENA & LATAM)",
            "Digital Transformation & Automation",
            "App & Web Development (Python, Flutter)",
            "Public Speaking & Stakeholder Engagement"
        ]
        
        for comp in competencies:
            p = doc.add_paragraph(style='List Bullet')
            run = p.add_run(comp)
            run.font.size = Pt(10)
            run.font.name = 'Calibri'
        
        # Languages
        lang_heading = doc.add_paragraph()
        lang_run = lang_heading.add_run("LANGUAGES")
        lang_run.font.bold = True
        lang_run.font.size = Pt(12)
        lang_run.font.name = 'Calibri'

        p = doc.add_paragraph(style='List Bullet')
        run = p.add_run("Arabic: Native | English: Fluent (Academic Level) | Spanish: Fluent | Portuguese: Basic | Urdu: Basic")
        run.font.size = Pt(10)
        run.font.name = 'Calibri'

        # Technical Skills
        tech_heading = doc.add_paragraph()
        tech_run = tech_heading.add_run("TECHNICAL & DIGITAL SKILLS")
        tech_run.font.bold = True
        tech_run.font.size = Pt(12)
        tech_run.font.name = 'Calibri'

        tech_skills = [
            "Programming & Development: Python (apps, automation, data tracking), Flutter (cross-platform mobile/web), Firebase (Auth, Firestore, Hosting)",
            "Web & eCommerce: WordPress/WooCommerce (eCommerce setup and SEO), Augment Auto Agent (AI-assisted app generation)",
            "Business Applications: Microsoft Office Suite (Advanced), CRM & ERP platforms (setup, training, integration)",
            "Design & Content: Canva (design, interactive kids' quizzes/games)"
        ]

        for skill in tech_skills:
            p = doc.add_paragraph(style='List Bullet')
            run = p.add_run(skill)
            run.font.size = Pt(10)
            run.font.name = 'Calibri'

        # Education
        edu_heading = doc.add_paragraph()
        edu_run = edu_heading.add_run("EDUCATION & CERTIFICATIONS")
        edu_run.font.bold = True
        edu_run.font.size = Pt(12)
        edu_run.font.name = 'Calibri'

        education_items = [
            "Diploma in Accounting – Arab Community College",
            "Diploma in Computer Applications",
            "Certified in Microsoft Office Suite"
        ]

        for edu in education_items:
            p = doc.add_paragraph(style='List Bullet')
            run = p.add_run(edu)
            run.font.size = Pt(10)
            run.font.name = 'Calibri'

        # Professional Experience
        exp_heading = doc.add_paragraph()
        exp_run = exp_heading.add_run("PROFESSIONAL EXPERIENCE")
        exp_run.font.bold = True
        exp_run.font.size = Pt(12)
        exp_run.font.name = 'Calibri'
        
        # Job experiences data
        jobs = [
            {
                "title": "Operation Manager – Commercial Segments, Revenue & All Operations",
                "company": "KAF Logistic & Transportation & Goods Clearance Services-FZE | Sharjah, UAE | Oct 2010 – Present",
                "bullets": [
                    "Excellent hands-on exposure and understanding of all key areas of Logistic operations",
                    "Implemented revenue management strategies and coached senior managers resulting in monthly revenue increase year on year",
                    "Completely restructured revenue generating departments",
                    "Representing the Company on all major Trade Exhibitions",
                    "Overall responsibility for managing operations of sales division (All segments) to maximize profitability, ensure superior service and product quality",
                    "Establish and manage required strategies to achieve agreed financial and non-financial targets",
                    "Identify market trends and performance to create and implement action plans to achieve greater profitability and increase business volume",
                    "Fully responsible for Profit and Loss management of the division",
                    "Successfully managing and providing strong leadership to a multi-cultural team, managing over 1300 employees"
                ]
            },
            {
                "title": "Sales Manager – Middle East",
                "company": "Reem Emirates Aluminum | Abu Dhabi, UAE | Sep 2008 – Aug 2010",
                "bullets": [
                    "Collect market intelligence & share with Operations Manager or Estimator as & when required",
                    "Identify New Clients & introduce COMPANY's services",
                    "Visit new Companies to introduce our Services",
                    "Liaise with Various Division to verify availability of resources before committing services to any clients",
                    "Prepare & submit Quotations",
                    "Enter into negotiations (as & when required)",
                    "Visit & discuss further & explain COMPANY's quality oriented program & convince clients of advantages of using our Services"
                ]
            },
            {
                "title": "Marketing Manager",
                "company": "M/S E.Frieg Transporte | Santiago, Chile | Jun 1994 – Nov 1997",
                "bullets": [
                    "Developed comprehensive marketing strategies for transportation and logistics services",
                    "Managed client relationships and coordinated service delivery across multiple routes",
                    "Conducted market analysis to identify growth opportunities in Chilean transport sector",
                    "Collaborated with operations team to ensure service quality and customer satisfaction"
                ]
            },
            {
                "title": "Salesman",
                "company": "M/S Al Noor Trading Company | Dubai, UAE | Jul 1990 – Mar 1994",
                "bullets": [
                    "Achieved consistent monthly sales targets across electronics and consumer goods",
                    "Built and maintained strong relationships with retail partners and distributors",
                    "Provided product demonstrations and technical support to customers",
                    "Assisted in inventory management and promotional campaign execution",
                    "Developed market knowledge of UAE consumer preferences and buying patterns"
                ]
            }
        ]
        
        for job in jobs:
            # Job title
            title_p = doc.add_paragraph()
            title_run = title_p.add_run(job["title"])
            title_run.font.bold = True
            title_run.font.size = Pt(11)
            title_run.font.name = 'Calibri'
            
            # Company info
            company_p = doc.add_paragraph()
            company_run = company_p.add_run(job["company"])
            company_run.font.italic = True
            company_run.font.size = Pt(9)
            company_run.font.name = 'Calibri'
            
            # Bullets
            for bullet in job["bullets"]:
                p = doc.add_paragraph(style='List Bullet')
                run = p.add_run(bullet)
                run.font.size = Pt(10)
                run.font.name = 'Calibri'

        # Notable Projects
        projects_heading = doc.add_paragraph()
        projects_run = projects_heading.add_run("NOTABLE PROJECTS & DIGITAL INITIATIVES")
        projects_run.font.bold = True
        projects_run.font.size = Pt(12)
        projects_run.font.name = 'Calibri'

        projects = [
            "Developed SAT Exam Simulator app using Flutter with exam/practice modes, real-time timer, and local data saving",
            "Created multiple Excel-based tools (Invoice Generator, Attendance Tracker) using Python automation",
            "Launched and manage Digistore3, a full digital product store with SEO-optimized listings and eCommerce functionality using WordPress & WooCommerce"
        ]

        for project in projects:
            p = doc.add_paragraph(style='List Bullet')
            run = p.add_run(project)
            run.font.size = Pt(10)
            run.font.name = 'Calibri'

        # Save document
        doc.save(docx_file)
        print(f"✅ Word document created: {docx_file}")
        return True
        
    except Exception as e:
        print(f"❌ Error creating Word document: {e}")
        return False

def main():
    """Main function"""
    print("🚀 Starting resume conversion...")

    # Install requirements
    install_requirements()

    # File paths
    html_file = "Raid_Fodeh_Resume.html"
    print_html_file = "Raid_Fodeh_Resume_Print.html"
    docx_file = "Raid_Fodeh_Resume.docx"

    # Check if HTML file exists
    if not os.path.exists(html_file):
        print(f"❌ HTML file not found: {html_file}")
        return

    # Create print-ready HTML
    print("\n📄 Creating print-ready HTML...")
    create_print_html(html_file, print_html_file)

    # Convert to Word
    print("\n📝 Converting to Word...")
    html_to_docx(html_file, docx_file)

    print("\n✅ Resume conversion completed!")
    print(f"📁 Files created:")
    print(f"   • {print_html_file} (open in browser and print to PDF)")
    print(f"   • {docx_file}")

if __name__ == "__main__":
    main()
